@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* Modern Color Palette - Light Mode */
  --background: oklch(0.99 0.005 106);
  --foreground: oklch(0.15 0.02 258);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 258);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 258);

  /* Primary - Modern Blue */
  --primary: oklch(0.45 0.15 258);
  --primary-foreground: oklch(0.98 0.005 258);

  /* Secondary - Warm Gray */
  --secondary: oklch(0.96 0.01 258);
  --secondary-foreground: oklch(0.25 0.02 258);

  /* Muted - Subtle Gray */
  --muted: oklch(0.95 0.01 258);
  --muted-foreground: oklch(0.55 0.015 258);

  /* Accent - Electric Cyan */
  --accent: oklch(0.7 0.15 195);
  --accent-foreground: oklch(0.98 0.005 195);

  /* Success - Modern Green */
  --success: oklch(0.6 0.15 142);
  --success-foreground: oklch(0.98 0.005 142);

  /* Warning - Vibrant Orange */
  --warning: oklch(0.7 0.15 65);
  --warning-foreground: oklch(0.15 0.02 65);

  /* Destructive - Modern Red */
  --destructive: oklch(0.6 0.2 25);
  --destructive-foreground: oklch(0.98 0.005 25);

  /* Borders and Inputs */
  --border: oklch(0.9 0.01 258);
  --input: oklch(0.92 0.01 258);
  --ring: oklch(0.45 0.15 258);

  /* Chart Colors */
  --chart-1: oklch(0.6 0.15 258);
  --chart-2: oklch(0.65 0.15 195);
  --chart-3: oklch(0.6 0.15 142);
  --chart-4: oklch(0.7 0.15 65);
  --chart-5: oklch(0.6 0.2 25);

  /* Sidebar */
  --sidebar: oklch(0.98 0.005 258);
  --sidebar-foreground: oklch(0.15 0.02 258);
  --sidebar-primary: oklch(0.45 0.15 258);
  --sidebar-primary-foreground: oklch(0.98 0.005 258);
  --sidebar-accent: oklch(0.95 0.01 258);
  --sidebar-accent-foreground: oklch(0.25 0.02 258);
  --sidebar-border: oklch(0.9 0.01 258);
  --sidebar-ring: oklch(0.45 0.15 258);

  /* Gradient Variables */
  --gradient-primary: linear-gradient(135deg, oklch(0.45 0.15 258), oklch(0.7 0.15 195));
  --gradient-secondary: linear-gradient(135deg, oklch(0.7 0.15 195), oklch(0.6 0.15 142));
  --gradient-accent: linear-gradient(135deg, oklch(0.6 0.15 142), oklch(0.7 0.15 65));
}

.dark {
  /* Modern Color Palette - Dark Mode */
  --background: oklch(0.08 0.01 258);
  --foreground: oklch(0.95 0.005 258);
  --card: oklch(0.12 0.015 258);
  --card-foreground: oklch(0.95 0.005 258);
  --popover: oklch(0.12 0.015 258);
  --popover-foreground: oklch(0.95 0.005 258);

  /* Primary - Bright Blue */
  --primary: oklch(0.65 0.2 258);
  --primary-foreground: oklch(0.08 0.01 258);

  /* Secondary - Dark Gray */
  --secondary: oklch(0.18 0.015 258);
  --secondary-foreground: oklch(0.9 0.005 258);

  /* Muted - Medium Gray */
  --muted: oklch(0.15 0.015 258);
  --muted-foreground: oklch(0.65 0.01 258);

  /* Accent - Bright Cyan */
  --accent: oklch(0.75 0.2 195);
  --accent-foreground: oklch(0.08 0.01 195);

  /* Success - Bright Green */
  --success: oklch(0.7 0.2 142);
  --success-foreground: oklch(0.08 0.01 142);

  /* Warning - Bright Orange */
  --warning: oklch(0.75 0.2 65);
  --warning-foreground: oklch(0.08 0.01 65);

  /* Destructive - Bright Red */
  --destructive: oklch(0.7 0.25 25);
  --destructive-foreground: oklch(0.95 0.005 25);

  /* Borders and Inputs */
  --border: oklch(0.25 0.02 258);
  --input: oklch(0.2 0.015 258);
  --ring: oklch(0.65 0.2 258);

  /* Chart Colors - Dark Mode */
  --chart-1: oklch(0.65 0.2 258);
  --chart-2: oklch(0.75 0.2 195);
  --chart-3: oklch(0.7 0.2 142);
  --chart-4: oklch(0.75 0.2 65);
  --chart-5: oklch(0.7 0.25 25);

  /* Sidebar - Dark Mode */
  --sidebar: oklch(0.1 0.01 258);
  --sidebar-foreground: oklch(0.95 0.005 258);
  --sidebar-primary: oklch(0.65 0.2 258);
  --sidebar-primary-foreground: oklch(0.08 0.01 258);
  --sidebar-accent: oklch(0.15 0.015 258);
  --sidebar-accent-foreground: oklch(0.9 0.005 258);
  --sidebar-border: oklch(0.25 0.02 258);
  --sidebar-ring: oklch(0.65 0.2 258);

  /* Gradient Variables - Dark Mode */
  --gradient-primary: linear-gradient(135deg, oklch(0.65 0.2 258), oklch(0.75 0.2 195));
  --gradient-secondary: linear-gradient(135deg, oklch(0.75 0.2 195), oklch(0.7 0.2 142));
  --gradient-accent: linear-gradient(135deg, oklch(0.7 0.2 142), oklch(0.75 0.2 65));
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Modern Typography Scale */
  h1 {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight;
    line-height: 1.1;
  }

  h2 {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight;
    line-height: 1.2;
  }

  h3 {
    @apply text-2xl sm:text-3xl font-semibold tracking-tight;
    line-height: 1.3;
  }

  h4 {
    @apply text-xl sm:text-2xl font-semibold tracking-tight;
    line-height: 1.4;
  }

  h5 {
    @apply text-lg sm:text-xl font-medium;
    line-height: 1.5;
  }

  h6 {
    @apply text-base sm:text-lg font-medium;
    line-height: 1.5;
  }

  p {
    @apply text-base leading-relaxed;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }
}

@layer components {
  /* Modern Gradient Backgrounds */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }

  /* Modern shadows */
  .shadow-modern {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .shadow-modern-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  .shadow-modern-xl {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  /* Animated gradients */
  .animated-gradient {
    background: linear-gradient(-45deg,
      oklch(0.45 0.15 258),
      oklch(0.7 0.15 195),
      oklch(0.6 0.15 142),
      oklch(0.7 0.15 65)
    );
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Modern button styles */
  .btn-modern {
    @apply relative overflow-hidden rounded-xl px-6 py-3 font-semibold transition-all duration-300;
    @apply bg-primary text-primary-foreground hover:scale-105 hover:shadow-modern-lg;
  }

  .btn-modern::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  .btn-modern:hover::before {
    transform: translateX(100%);
  }

  /* Text effects */
  .text-gradient {
    @apply bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent;
    background-size: 200% auto;
    animation: shimmer 3s linear infinite;
  }

  @keyframes shimmer {
    to { background-position: 200% center; }
  }

  /* Modern card styles */
  .card-modern {
    @apply relative rounded-2xl border border-border/50 bg-card/50 backdrop-blur-sm;
    @apply shadow-modern hover:shadow-modern-lg transition-all duration-300;
  }

  .card-modern::before {
    content: '';
    @apply absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none;
  }

  /* Scroll animations */
  .fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  .pulse-dot {
    @apply w-2 h-2 bg-primary rounded-full animate-pulse;
  }
}

