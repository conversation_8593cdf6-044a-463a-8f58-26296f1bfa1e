// app/layout.tsx
import './globals.css';
import { Inter } from 'next/font/google';
import { cn } from '@/lib/utils';
import { ThemeProvider } from '@/components/ThemeProvider';
import { Navbar } from '@/components/NavBar';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Sameer Dev | Full Stack Developer',
  description: 'I build scalable web apps with MERN, Next.js, and AI integrations.',
  openGraph: {
    title: 'Sameer Dev Portfolio',
    description: 'Full stack portfolio with projects, contact, and about info.',
    url: 'https://sameer-dev.online', // Update this
    siteName: 'sameer-dev.online',
    images: [
      {
        url: '/og-image.png', // Add your OG image in public folder
        width: 1200,
        height: 630,
        alt: 'Sameer Dev Portfolio Website',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, 'bg-background text-foreground')}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <Navbar />
          <main className="pt-16">{children}</main>
        </ThemeProvider>
      </body>
    </html>
  );
}
