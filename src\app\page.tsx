'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Mail, Github, Linkedin } from "lucide-react";
import { motion } from "framer-motion";
import Link from "next/link";


export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col justify-center items-center px-6 bg-gradient-to-b from-white via-gray-50 to-gray-100 dark:from-black dark:via-neutral-900 dark:to-neutral-950">
      
      {/* Hero Section */}
      <motion.div
        className="text-center max-w-3xl"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
      >
        <Badge className="mb-4 text-sm" variant="outline">
          Full Stack Developer
        </Badge>

        <h1 className="text-4xl sm:text-5xl font-bold tracking-tight mb-6">
          Hello, I am <span className="text-primary">Xmart</span><br />
          I Build Smart, Scalable Web Apps.
        </h1>

        <p className="text-muted-foreground mb-8 text-lg">
          MERN Stack | Next.js | TypeScript | AI Integrations | CRM | TailwindCSS
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/projects">
            <Button className="px-6">
              View Projects <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link href="/contact">
            <Button variant="outline" className="px-6">
              Contact Me <Mail className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </motion.div>

      {/* Social Icons */}
      <motion.div
        className="mt-12 flex gap-6 text-muted-foreground"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <a href="https://github.com/yourgithub" target="_blank" rel="noreferrer">
          <Github className="w-5 h-5 hover:text-primary" />
        </a>
        <a href="https://linkedin.com/in/yourprofile" target="_blank" rel="noreferrer">
          <Linkedin className="w-5 h-5 hover:text-primary" />
        </a>
        <a href="mailto:<EMAIL>">
          <Mail className="w-5 h-5 hover:text-primary" />
        </a>
      </motion.div>
    </div>
  );
}
