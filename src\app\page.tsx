'use client';

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Mail, Code, Sparkles, GitBranch, User } from "lucide-react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useEffect, useState } from "react";

interface TypewriterProps {
  words: string[];
  delay?: number;
}

function Typewriter({ words, delay = 2000 }: TypewriterProps) {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const currentWord = words[currentWordIndex];
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        if (currentText.length < currentWord.length) {
          setCurrentText(currentWord.slice(0, currentText.length + 1));
        } else {
          setTimeout(() => setIsDeleting(true), delay);
        }
      } else {
        if (currentText.length > 0) {
          setCurrentText(currentText.slice(0, -1));
        } else {
          setIsDeleting(false);
          setCurrentWordIndex((prev) => (prev + 1) % words.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [currentText, isDeleting, currentWordIndex, words, delay]);

  return (
    <span className="text-primary">
      {currentText}
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.8, repeat: Infinity }}
        className="ml-1"
      >
        |
      </motion.span>
    </span>
  );
}

function FloatingElement({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) {
  return (
    <motion.div
      animate={{
        y: [-10, 10, -10],
        rotate: [-5, 5, -5],
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut",
        delay,
      }}
      className="absolute opacity-20 dark:opacity-10"
    >
      {children}
    </motion.div>
  );
}

export default function HomePage() {
  const skills = ["Full Stack Developer", "React Specialist", "TypeScript Expert", "AI Integrator"];

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Modern Animated Background */}
      <div className="absolute inset-0">
        {/* Base gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background/90" />

        {/* Animated gradient overlay */}
        <div className="absolute inset-0 animated-gradient opacity-30" />

        {/* Geometric patterns */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-primary/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-primary/5 to-accent/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
        </div>

        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, currentColor 1px, transparent 0)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* Dynamic Floating Elements */}
      <FloatingElement delay={0}>
        <div className="absolute top-[20%] left-[10%] p-4 rounded-2xl bg-primary/10 backdrop-blur-sm border border-primary/20">
          <Code className="w-8 h-8 text-primary" />
        </div>
      </FloatingElement>
      <FloatingElement delay={2}>
        <div className="absolute top-[60%] right-[15%] p-3 rounded-xl bg-accent/10 backdrop-blur-sm border border-accent/20">
          <Sparkles className="w-6 h-6 text-accent" />
        </div>
      </FloatingElement>
      <FloatingElement delay={4}>
        <div className="absolute top-[30%] right-[20%] p-3 rounded-xl bg-success/10 backdrop-blur-sm border border-success/20">
          <GitBranch className="w-7 h-7 text-success" />
        </div>
      </FloatingElement>

      {/* Main Content - Split Screen Layout */}
      <div className="relative z-10 min-h-screen">
        <div className="container mx-auto px-6 h-screen flex items-center">
          <div className="grid lg:grid-cols-2 gap-12 items-center w-full">

            {/* Left Side - Content */}
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              {/* Status Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="inline-block"
              >
                <Badge className="text-sm px-4 py-2 bg-gradient-to-r from-primary/10 to-accent/10 text-primary border border-primary/20 rounded-full">
                  <Sparkles className="w-3 h-3 mr-2" />
                  Available for Projects
                </Badge>
              </motion.div>

              {/* Main Heading */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.8 }}
                className="space-y-4"
              >
                <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold tracking-tight leading-[1.1]">
                  Hello, I&apos;m{" "}
                  <span className="text-gradient">
                    Xmart
                  </span>
                </h1>
                <div className="text-2xl sm:text-3xl lg:text-4xl text-muted-foreground font-medium">
                  I&apos;m a{" "}
                  <span className="text-primary font-semibold">
                    <Typewriter words={skills} />
                  </span>
                </div>
              </motion.div>

              {/* Description */}
              <motion.p
                className="text-muted-foreground text-lg sm:text-xl leading-relaxed max-w-xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
              >
                I build modern, scalable web applications using{" "}
                <span className="text-primary font-semibold">MERN Stack</span>,{" "}
                <span className="text-accent font-semibold">Next.js</span>, and{" "}
                <span className="text-success font-semibold">AI integrations</span>.
                Let&apos;s create something amazing together.
              </motion.p>

              {/* Action Buttons */}
              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.8 }}
              >
                <Link href="/projects">
                  <Button size="lg" className="btn-modern group">
                    View My Work
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button variant="outline" size="lg" className="px-8 py-3 rounded-xl border-2 hover:bg-accent/10 hover:border-accent group">
                    Let&apos;s Talk
                    <Mail className="ml-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  </Button>
                </Link>
              </motion.div>

              {/* Quick Stats */}
              <motion.div
                className="flex gap-8 pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1, duration: 0.8 }}
              >
                {[
                  { number: "50+", label: "Projects" },
                  { number: "3+", label: "Years Experience" },
                  { number: "100%", label: "Client Satisfaction" }
                ].map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl font-bold text-primary">{stat.number}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </div>
                ))}
              </motion.div>
            </motion.div>

            {/* Right Side - Visual Element */}
            <motion.div
              className="hidden lg:flex items-center justify-center"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            >
              <div className="relative w-96 h-96">
                {/* Central Avatar/Logo */}
                <motion.div
                  className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 via-accent/20 to-success/20 backdrop-blur-sm border border-primary/30"
                  animate={{
                    rotate: [0, 360],
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />

                {/* Inner circle with code icon */}
                <div className="absolute inset-8 rounded-full bg-background/80 backdrop-blur-md border border-border/50 flex items-center justify-center">
                  <Code className="w-24 h-24 text-primary" />
                </div>

                {/* Orbiting elements */}
                {[
                  { icon: GitBranch, delay: 0, radius: 140, color: "text-primary" },
                  { icon: User, delay: 2, radius: 160, color: "text-accent" },
                  { icon: Sparkles, delay: 4, radius: 180, color: "text-success" },
                ].map(({ icon: Icon, delay, radius, color }, index) => (
                  <motion.div
                    key={index}
                    className={`absolute w-12 h-12 rounded-full bg-background/90 backdrop-blur-sm border border-border/50 flex items-center justify-center ${color}`}
                    style={{
                      top: '50%',
                      left: '50%',
                    }}
                    animate={{
                      rotate: [0, 360],
                      x: [radius, radius],
                      y: [0, 0],
                    }}
                    transition={{
                      duration: 15 + delay,
                      repeat: Infinity,
                      ease: "linear",
                      delay: delay
                    }}
                  >
                    <Icon className="w-6 h-6" />
                  </motion.div>
                ))}

                {/* Floating particles */}
                {Array.from({ length: 6 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 rounded-full bg-primary/40"
                    style={{
                      top: `${20 + (i * 10)}%`,
                      left: `${15 + (i * 12)}%`,
                    }}
                    animate={{
                      y: [-10, 10, -10],
                      opacity: [0.4, 1, 0.4],
                    }}
                    transition={{
                      duration: 3 + i,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: i * 0.5
                    }}
                  />
                ))}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Social Links - Bottom */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.8 }}
        >
          {[
            { icon: GitBranch, href: "https://github.com/yourgithub", label: "GitHub" },
            { icon: User, href: "https://linkedin.com/in/yourprofile", label: "LinkedIn" },
            { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
          ].map(({ icon: Icon, href, label }) => (
            <motion.a
              key={label}
              href={href}
              target={href.startsWith('mailto:') ? undefined : "_blank"}
              rel={href.startsWith('mailto:') ? undefined : "noreferrer"}
              className="p-3 rounded-full bg-background/80 backdrop-blur-sm border border-border/50 hover:bg-primary/10 text-muted-foreground hover:text-primary transition-all duration-300 group"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              aria-label={label}
            >
              <Icon className="w-5 h-5 group-hover:scale-110 transition-transform" />
            </motion.a>
          ))}
        </motion.div>

      </div>
    </div>
  );
}
