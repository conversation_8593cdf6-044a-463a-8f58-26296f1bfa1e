'use client';

import { <PERSON>, CardContent, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { ExternalLink, GitBranch, Calendar, Star, Filter, Code } from "lucide-react";
import type { Project, ProjectCategory } from "@/types";

const projects: Project[] = [
  {
    id: "true-feedback",
    title: "True Feedback",
    description: "Anonymous AI-powered feedback platform with real-time analytics",
    longDescription: "A comprehensive feedback platform that leverages OpenAI for sentiment analysis and provides anonymous feedback collection with real-time dashboard analytics.",
    image: "/api/placeholder/600/400",
    technologies: ["Next.js", "OpenAI", "TypeScript", "Tailwind CSS", "MongoDB", "NextAuth"],
    category: "AI/ML",
    liveUrl: "https://true-feedback-dun.vercel.app/",
    repoUrl: "https://github.com/username/true-feedback",
    featured: true,
    completedDate: "2024-01",
    status: "completed"
  },
  {
    id: "mega-mall",
    title: "Mega Mall",
    description: "Full-featured e-commerce platform with advanced shopping capabilities",
    longDescription: "A complete e-commerce solution featuring product catalog, shopping cart, secure checkout, user reviews, order tracking, and admin dashboard.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "Node.js", "Express", "MongoDB", "Stripe", "JWT"],
    category: "E-commerce",
    liveUrl: "https://mega-mall.vercel.app/",
    repoUrl: "https://github.com/username/mega-mall",
    featured: true,
    completedDate: "2023-12",
    status: "completed"
  },
  {
    id: "portfolio-v2",
    title: "Portfolio Website",
    description: "Modern portfolio website with dark mode and animations",
    longDescription: "A responsive portfolio website built with Next.js 15, featuring modern animations, dark mode support, and optimized performance.",
    image: "/api/placeholder/600/400",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Framer Motion", "Shadcn/ui"],
    category: "Web Development",
    liveUrl: "#",
    repoUrl: "https://github.com/username/portfolio",
    featured: false,
    completedDate: "2024-01",
    status: "completed"
  },
  {
    id: "ai-chat-app",
    title: "AI Chat Assistant",
    description: "Intelligent chat application with multiple AI model support",
    longDescription: "A sophisticated chat application supporting multiple AI models with conversation history, file uploads, and custom prompt templates.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "Python", "FastAPI", "OpenAI", "LangChain", "PostgreSQL"],
    category: "AI/ML",
    liveUrl: "#",
    repoUrl: "https://github.com/username/ai-chat",
    featured: false,
    completedDate: "2024-02",
    status: "in-progress"
  }
];

const categories: (ProjectCategory | "All")[] = ["All", "AI/ML", "E-commerce", "Web Development", "Mobile"];

interface ProjectCardProps {
  project: Project;
  index: number;
}

function ProjectCard({ project, index }: ProjectCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Dynamic height based on content
  const getCardHeight = () => {
    const baseHeight = 300;
    const descriptionLength = project.description.length;
    const techCount = project.technologies.length;
    return baseHeight + (descriptionLength > 100 ? 50 : 0) + (techCount > 4 ? 30 : 0);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.6 }}
      whileHover={{ scale: 1.02 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      style={{ height: `${getCardHeight()}px` }}
    >
      <Card className="card-modern h-full overflow-hidden group relative">
        {/* Project Image/Visual */}
        <div className="relative h-40 overflow-hidden bg-gradient-to-br from-primary/10 via-accent/5 to-success/10">
          {/* Animated background pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 transform rotate-12 scale-150" />
          </div>

          {/* Project preview mockup */}
          <div className="absolute inset-4 bg-background/90 rounded-lg border border-border/50 flex items-center justify-center">
            <div className="text-center">
              <Code className="w-8 h-8 text-primary mx-auto mb-2" />
              <div className="text-xs text-muted-foreground">{project.category}</div>
            </div>
          </div>

          {/* Status badges */}
          <div className="absolute top-3 left-3 flex gap-2">
            {project.featured && (
              <Badge className="bg-primary/90 text-primary-foreground border-0 shadow-lg">
                <Star className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
            <Badge
              className={`border-0 shadow-lg ${
                project.status === 'completed' ? 'bg-success/90 text-white' :
                project.status === 'in-progress' ? 'bg-warning/90 text-white' :
                'bg-accent/90 text-white'
              }`}
            >
              {project.status === 'completed' ? 'Live' :
               project.status === 'in-progress' ? 'Building' : 'Planned'}
            </Badge>
          </div>

          {/* Hover overlay with quick actions */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex items-end justify-center pb-4 gap-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <Button size="sm" className="bg-white/90 text-black hover:bg-white" asChild>
              <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                View Live
              </a>
            </Button>
            <Button size="sm" variant="outline" className="border-white/50 text-white hover:bg-white/10" asChild>
              <a href={project.repoUrl} target="_blank" rel="noopener noreferrer">
                <GitBranch className="w-4 h-4 mr-2" />
                Code
              </a>
            </Button>
          </motion.div>
        </div>

        <CardContent className="p-5 flex-1 flex flex-col">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors leading-tight">
              {project.title}
            </CardTitle>
            <div className="flex items-center text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-full">
              <Calendar className="w-3 h-3 mr-1" />
              {project.completedDate}
            </div>
          </div>

          {/* Description */}
          <p className="text-muted-foreground text-sm mb-4 leading-relaxed flex-1">
            {project.description}
          </p>

          {/* Technology Stack */}
          <div className="flex flex-wrap gap-1.5 mb-4">
            {project.technologies.slice(0, 3).map((tech) => (
              <Badge
                key={tech}
                variant="secondary"
                className="text-xs px-2 py-1 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors"
              >
                {tech}
              </Badge>
            ))}
            {project.technologies.length > 3 && (
              <Badge
                variant="outline"
                className="text-xs px-2 py-1 border-dashed"
              >
                +{project.technologies.length - 3} more
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            <Button size="sm" className="flex-1 rounded-xl" asChild>
              <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                Live Demo
              </a>
            </Button>
            <Button size="sm" variant="outline" className="rounded-xl" asChild>
              <a href={project.repoUrl} target="_blank" rel="noopener noreferrer">
                <GitBranch className="w-4 h-4" />
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export default function ProjectsPage() {
  const [selectedCategory, setSelectedCategory] = useState<ProjectCategory | "All">("All");
  const [showFeaturedOnly, setShowFeaturedOnly] = useState<boolean>(false);

  const filteredProjects = projects.filter(project => {
    const categoryMatch = selectedCategory === "All" || project.category === selectedCategory;
    const featuredMatch = !showFeaturedOnly || project.featured;
    return categoryMatch && featuredMatch;
  });

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl sm:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            My Projects
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            A collection of projects showcasing my skills in full-stack development,
            AI integration, and modern web technologies.
          </p>
        </motion.div>

        {/* Filters */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 mb-8 items-center justify-between"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="transition-all duration-200"
              >
                <Filter className="w-3 h-3 mr-2" />
                {category}
              </Button>
            ))}
          </div>

          <Button
            variant={showFeaturedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
            className="transition-all duration-200"
          >
            <Star className="w-3 h-3 mr-2" />
            Featured Only
          </Button>
        </motion.div>

        {/* Modern Projects Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedCategory + showFeaturedOnly}
            className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {filteredProjects.map((project, index) => (
              <div key={project.id} className="break-inside-avoid mb-6">
                <ProjectCard project={project} index={index} />
              </div>
            ))}
          </motion.div>
        </AnimatePresence>

        {filteredProjects.length === 0 && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <p className="text-muted-foreground text-lg">
              No projects found for the selected filters.
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
}
