'use client';

import { <PERSON>, CardContent, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { ExternalLink, GitBranch, Calendar, Star, Filter } from "lucide-react";

interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  image: string;
  technologies: string[];
  category: string;
  liveUrl: string;
  repoUrl: string;
  featured: boolean;
  completedDate: string;
  status: 'completed' | 'in-progress' | 'planned';
}

const projects: Project[] = [
  {
    id: "true-feedback",
    title: "True Feedback",
    description: "Anonymous AI-powered feedback platform with real-time analytics",
    longDescription: "A comprehensive feedback platform that leverages OpenAI for sentiment analysis and provides anonymous feedback collection with real-time dashboard analytics.",
    image: "/api/placeholder/600/400",
    technologies: ["Next.js", "OpenAI", "TypeScript", "Tailwind CSS", "MongoDB", "NextAuth"],
    category: "AI/ML",
    liveUrl: "https://true-feedback-dun.vercel.app/",
    repoUrl: "https://github.com/username/true-feedback",
    featured: true,
    completedDate: "2024-01",
    status: "completed"
  },
  {
    id: "mega-mall",
    title: "Mega Mall",
    description: "Full-featured e-commerce platform with advanced shopping capabilities",
    longDescription: "A complete e-commerce solution featuring product catalog, shopping cart, secure checkout, user reviews, order tracking, and admin dashboard.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "Node.js", "Express", "MongoDB", "Stripe", "JWT"],
    category: "E-commerce",
    liveUrl: "https://mega-mall.vercel.app/",
    repoUrl: "https://github.com/username/mega-mall",
    featured: true,
    completedDate: "2023-12",
    status: "completed"
  },
  {
    id: "portfolio-v2",
    title: "Portfolio Website",
    description: "Modern portfolio website with dark mode and animations",
    longDescription: "A responsive portfolio website built with Next.js 15, featuring modern animations, dark mode support, and optimized performance.",
    image: "/api/placeholder/600/400",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Framer Motion", "Shadcn/ui"],
    category: "Web Development",
    liveUrl: "#",
    repoUrl: "https://github.com/username/portfolio",
    featured: false,
    completedDate: "2024-01",
    status: "completed"
  },
  {
    id: "ai-chat-app",
    title: "AI Chat Assistant",
    description: "Intelligent chat application with multiple AI model support",
    longDescription: "A sophisticated chat application supporting multiple AI models with conversation history, file uploads, and custom prompt templates.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "Python", "FastAPI", "OpenAI", "LangChain", "PostgreSQL"],
    category: "AI/ML",
    liveUrl: "#",
    repoUrl: "https://github.com/username/ai-chat",
    featured: false,
    completedDate: "2024-02",
    status: "in-progress"
  }
];

const categories = ["All", "AI/ML", "E-commerce", "Web Development", "Mobile"];

interface ProjectCardProps {
  project: Project;
  index: number;
}

function ProjectCard({ project, index }: ProjectCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.6 }}
      whileHover={{ y: -5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className="h-full overflow-hidden group hover:shadow-xl transition-all duration-300 border-0 bg-card/50 backdrop-blur-sm">
        {/* Project Image */}
        <div className="relative h-48 overflow-hidden bg-gradient-to-br from-primary/10 to-primary/5">
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          <div className="absolute top-4 left-4 flex gap-2">
            {project.featured && (
              <Badge className="bg-primary/20 text-primary border-primary/30">
                <Star className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
            <Badge
              variant="outline"
              className={`${
                project.status === 'completed' ? 'bg-green-500/20 text-green-600 border-green-500/30' :
                project.status === 'in-progress' ? 'bg-yellow-500/20 text-yellow-600 border-yellow-500/30' :
                'bg-blue-500/20 text-blue-600 border-blue-500/30'
              }`}
            >
              {project.status === 'completed' ? 'Completed' :
               project.status === 'in-progress' ? 'In Progress' : 'Planned'}
            </Badge>
          </div>

          {/* Hover overlay with quick actions */}
          <motion.div
            className="absolute inset-0 bg-black/60 flex items-center justify-center gap-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <Button size="sm" asChild>
              <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                Live Demo
              </a>
            </Button>
            <Button size="sm" variant="outline" asChild>
              <a href={project.repoUrl} target="_blank" rel="noopener noreferrer">
                <GitBranch className="w-4 h-4 mr-2" />
                Code
              </a>
            </Button>
          </motion.div>
        </div>

        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-3">
            <CardTitle className="text-xl group-hover:text-primary transition-colors">
              {project.title}
            </CardTitle>
            <div className="flex items-center text-xs text-muted-foreground">
              <Calendar className="w-3 h-3 mr-1" />
              {project.completedDate}
            </div>
          </div>

          <p className="text-muted-foreground mb-4 line-clamp-2">
            {project.description}
          </p>

          {/* Technology Stack */}
          <div className="flex flex-wrap gap-1 mb-4">
            {project.technologies.slice(0, 4).map((tech) => (
              <Badge key={tech} variant="secondary" className="text-xs">
                {tech}
              </Badge>
            ))}
            {project.technologies.length > 4 && (
              <Badge variant="secondary" className="text-xs">
                +{project.technologies.length - 4}
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button size="sm" className="flex-1" asChild>
              <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                View Live
              </a>
            </Button>
            <Button size="sm" variant="outline" asChild>
              <a href={project.repoUrl} target="_blank" rel="noopener noreferrer">
                <GitBranch className="w-4 h-4" />
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export default function ProjectsPage() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  const filteredProjects = projects.filter(project => {
    const categoryMatch = selectedCategory === "All" || project.category === selectedCategory;
    const featuredMatch = !showFeaturedOnly || project.featured;
    return categoryMatch && featuredMatch;
  });

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl sm:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            My Projects
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            A collection of projects showcasing my skills in full-stack development,
            AI integration, and modern web technologies.
          </p>
        </motion.div>

        {/* Filters */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 mb-8 items-center justify-between"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="transition-all duration-200"
              >
                <Filter className="w-3 h-3 mr-2" />
                {category}
              </Button>
            ))}
          </div>

          <Button
            variant={showFeaturedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
            className="transition-all duration-200"
          >
            <Star className="w-3 h-3 mr-2" />
            Featured Only
          </Button>
        </motion.div>

        {/* Projects Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedCategory + showFeaturedOnly}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {filteredProjects.map((project, index) => (
              <ProjectCard key={project.id} project={project} index={index} />
            ))}
          </motion.div>
        </AnimatePresence>

        {filteredProjects.length === 0 && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <p className="text-muted-foreground text-lg">
              No projects found for the selected filters.
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
}
