'use client';

import { Card, CardContent, CardTitle } from "@/components/ui/card";

export default function ProjectsPage() {
  const projects = [
    {
      title: "True Feedback",
      description: "Anonymous AI Feedback Web App using OpenAI, React, and TailwindCSS",
      link: "https://true-feedback-dun.vercel.app/",
      repo: "https://github.com/username/true-feedback"
    },
    {
      title: "Mega Mall",
      description: "Full-fledged e-commerce site with cart, checkout, reviews, etc.",
      link: "https://mega-mall.vercel.app/",
      repo: "https://github.com/username/mega-mall"
    }
    // Add more...
  ];

  return (
    <div className="max-w-5xl mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-6">Projects</h1>
      <div className="grid md:grid-cols-2 gap-6">
        {projects.map((project, i) => (
          <Card key={i}>
            <CardContent className="py-6">
              <CardTitle className="mb-2">{project.title}</CardTitle>
              <p className="text-muted-foreground mb-4">{project.description}</p>
              <div className="flex gap-4">
                <a href={project.link} target="_blank" className="text-blue-600 hover:underline">Live</a>
                <a href={project.repo} target="_blank" className="text-muted-foreground hover:underline">Code</a>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
