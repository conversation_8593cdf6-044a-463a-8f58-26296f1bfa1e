'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMousePosition } from '@/hooks/useScrollAnimation';

interface CursorState {
  isHovering: boolean;
  isClicking: boolean;
  cursorType: 'default' | 'pointer' | 'text' | 'grab' | 'grabbing';
  text?: string;
}

export function InteractiveCursor() {
  const mousePosition = useMousePosition();
  const [cursorState, setCursorState] = useState<CursorState>({
    isHovering: false,
    isClicking: false,
    cursorType: 'default'
  });

  useEffect(() => {
    const handleMouseDown = () => setCursorState(prev => ({ ...prev, isClicking: true }));
    const handleMouseUp = () => setCursorState(prev => ({ ...prev, isClicking: false }));

    const handleMouseEnter = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      
      if (target.matches('a, button, [role="button"], input, textarea, select')) {
        setCursorState(prev => ({ 
          ...prev, 
          isHovering: true, 
          cursorType: 'pointer',
          text: target.getAttribute('data-cursor-text') || undefined
        }));
      } else if (target.matches('input[type="text"], textarea, [contenteditable]')) {
        setCursorState(prev => ({ ...prev, isHovering: true, cursorType: 'text' }));
      } else if (target.matches('[data-cursor="grab"]')) {
        setCursorState(prev => ({ ...prev, isHovering: true, cursorType: 'grab' }));
      }
    };

    const handleMouseLeave = () => {
      setCursorState(prev => ({ 
        ...prev, 
        isHovering: false, 
        cursorType: 'default',
        text: undefined
      }));
    };

    // Add event listeners to interactive elements
    const addListeners = () => {
      const interactiveElements = document.querySelectorAll(
        'a, button, [role="button"], input, textarea, select, [data-cursor]'
      );
      
      interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
      });
    };

    // Initial setup
    addListeners();

    // Re-add listeners when DOM changes
    const observer = new MutationObserver(addListeners);
    observer.observe(document.body, { childList: true, subtree: true });

    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      observer.disconnect();
    };
  }, []);

  // Don't render on mobile devices
  useEffect(() => {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
    
    if (isMobile) {
      return;
    }
  }, []);

  const getCursorSize = () => {
    switch (cursorState.cursorType) {
      case 'pointer':
        return cursorState.text ? 80 : 40;
      case 'text':
        return 20;
      case 'grab':
      case 'grabbing':
        return 50;
      default:
        return cursorState.isHovering ? 40 : 20;
    }
  };

  const getCursorColor = () => {
    switch (cursorState.cursorType) {
      case 'pointer':
        return 'rgba(59, 130, 246, 0.8)'; // Blue
      case 'text':
        return 'rgba(16, 185, 129, 0.8)'; // Green
      case 'grab':
        return 'rgba(245, 158, 11, 0.8)'; // Yellow
      case 'grabbing':
        return 'rgba(239, 68, 68, 0.8)'; // Red
      default:
        return 'rgba(156, 163, 175, 0.8)'; // Gray
    }
  };

  return (
    <motion.div
      className="fixed top-0 left-0 pointer-events-none z-50 mix-blend-difference"
      animate={{
        x: mousePosition.x - getCursorSize() / 2,
        y: mousePosition.y - getCursorSize() / 2,
      }}
      transition={{
        type: "spring",
        stiffness: 500,
        damping: 28,
        mass: 0.5
      }}
    >
      <motion.div
        className="relative rounded-full border-2 border-white"
        animate={{
          width: getCursorSize(),
          height: getCursorSize(),
          backgroundColor: getCursorColor(),
          scale: cursorState.isClicking ? 0.8 : 1,
        }}
        transition={{
          type: "spring",
          stiffness: 400,
          damping: 25
        }}
      >
        {/* Inner dot */}
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-full"
          animate={{
            width: cursorState.isHovering ? 4 : 8,
            height: cursorState.isHovering ? 4 : 8,
            opacity: cursorState.text ? 0 : 1
          }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 25
          }}
        />

        {/* Text label */}
        <AnimatePresence>
          {cursorState.text && (
            <motion.div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-xs font-medium whitespace-nowrap"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              {cursorState.text}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Ripple effect on click */}
        <AnimatePresence>
          {cursorState.isClicking && (
            <motion.div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 border-2 border-white rounded-full"
              initial={{ width: 0, height: 0, opacity: 1 }}
              animate={{ 
                width: getCursorSize() * 2, 
                height: getCursorSize() * 2, 
                opacity: 0 
              }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            />
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
}

// Hook to add cursor text to elements
export function useCursorText(text: string) {
  useEffect(() => {
    const element = document.activeElement as HTMLElement;
    if (element) {
      element.setAttribute('data-cursor-text', text);
      return () => {
        element.removeAttribute('data-cursor-text');
      };
    }
  }, [text]);
}
