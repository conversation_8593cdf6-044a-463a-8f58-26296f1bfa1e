"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "./ui/button";

export function Navbar() {
  const pathname = usePathname();
  const links = [
    { href: "/", label: "Home" },
    { href: "/projects", label: "Projects" },
    { href: "/about", label: "About" },
    { href: "/contact", label: "Contact" },
  ];

  return (
    <nav className="w-full py-4 px-6 flex justify-between items-center shadow-sm sticky top-0 z-50 bg-background">
      <Link href="/" className="font-bold text-lg">Xmart Dev</Link>
      <div className="space-x-4">
        {links.map(({ href, label }) => (
          <Link key={href} href={href}>
            <Button variant={pathname === href ? "default" : "ghost"}>{label}</Button>
          </Link>
        ))}
      </div>
    </nav>
  );
}
