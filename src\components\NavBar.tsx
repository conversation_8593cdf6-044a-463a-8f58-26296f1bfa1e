"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence, useScroll, useTransform } from "framer-motion";
import { Button } from "./ui/button";
import { ThemeToggle } from "./ThemeToggle";
import { Menu, X, ArrowUp } from "lucide-react";

interface NavLink {
  href: string;
  label: string;
  section?: string;
}

export function Navbar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  const [showScrollTop, setShowScrollTop] = useState(false);
  const { scrollYProgress } = useScroll();

  const links: NavLink[] = [
    { href: "/", label: "Home", section: "hero" },
    { href: "/projects", label: "Projects", section: "projects" },
    { href: "/about", label: "About", section: "about" },
    { href: "/contact", label: "Contact", section: "contact" },
  ];

  const navOpacity = useTransform(scrollYProgress, [0, 0.1], [0.95, 1]);
  const navScale = useTransform(scrollYProgress, [0, 0.1], [0.98, 1]);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;

      setScrolled(scrollTop > 20);
      setShowScrollTop(scrollTop > 500);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLinkClick = () => {
    setIsOpen(false);
  };

  const scrollToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const smoothScrollTo = useCallback((href: string) => {
    if (href.startsWith('/#')) {
      const element = document.querySelector(href.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        return;
      }
    }
    // For regular navigation, let Next.js handle it
  }, []);

  return (
    <>
      {/* Scroll Progress Bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-accent to-primary z-50"
        style={{
          scaleX: scrollYProgress,
          transformOrigin: "0%"
        }}
      />

      {/* Main Navigation */}
      <motion.nav
        className={`fixed top-2 left-1/2 transform -translate-x-1/2 z-40 transition-all duration-500 ${
          scrolled
            ? "w-11/12 max-w-4xl"
            : "w-full max-w-7xl"
        }`}
        style={{
          opacity: navOpacity,
          scale: navScale
        }}
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <div className={`
          relative rounded-2xl transition-all duration-500
          ${scrolled
            ? "bg-background/90 backdrop-blur-xl border border-border/50 shadow-modern-xl"
            : "bg-background/70 backdrop-blur-md border border-border/30 shadow-modern"
          }
        `}>
          {/* Animated background gradient */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/5 via-accent/5 to-primary/5 opacity-50" />

          <div className="relative px-6 py-4">
            <div className="flex justify-between items-center">
              {/* Logo */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  href="/"
                  className="font-bold text-xl text-gradient hover:scale-105 transition-transform"
                >
                  Xmart Dev
                </Link>
              </motion.div>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-2">
                {links.map(({ href, label }, index) => (
                  <motion.div
                    key={href}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href={href}
                      onClick={() => smoothScrollTo(href)}
                    >
                      <Button
                        variant={pathname === href ? "default" : "ghost"}
                        className={`
                          relative rounded-xl transition-all duration-300
                          ${pathname === href
                            ? "bg-primary/10 text-primary border border-primary/20"
                            : "hover:bg-accent/10 hover:text-accent"
                          }
                        `}
                      >
                        {label}
                        {pathname === href && (
                          <motion.div
                            className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/20 to-accent/20"
                            layoutId="activeNavTab"
                            initial={false}
                            transition={{ type: "spring", stiffness: 500, damping: 30 }}
                          />
                        )}
                      </Button>
                    </Link>
                  </motion.div>
                ))}
                <div className="w-px h-6 bg-border/50 mx-2" />
                <ThemeToggle />
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden flex items-center space-x-2">
                <ThemeToggle />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(!isOpen)}
                  aria-label="Toggle menu"
                  className="rounded-xl"
                >
                  <AnimatePresence mode="wait">
                    {isOpen ? (
                      <motion.div
                        key="close"
                        initial={{ rotate: -90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: 90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <X className="h-5 w-5" />
                      </motion.div>
                    ) : (
                      <motion.div
                        key="menu"
                        initial={{ rotate: 90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: -90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Menu className="h-5 w-5" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Button>
              </div>
            </div>
          </div>
        </div>

      </motion.nav>

      {/* Mobile Navigation Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-30 md:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Backdrop */}
            <div
              className="absolute inset-0 bg-black/20 backdrop-blur-sm"
              onClick={() => setIsOpen(false)}
            />

            {/* Mobile Menu */}
            <motion.div
              className="absolute top-20 left-4 right-4 bg-background/95 backdrop-blur-xl rounded-2xl border border-border/50 shadow-modern-xl overflow-hidden"
              initial={{ opacity: 0, scale: 0.95, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <div className="p-6 space-y-3">
                {links.map(({ href, label }, index) => (
                  <motion.div
                    key={href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                  >
                    <Link
                      href={href}
                      onClick={() => {
                        handleLinkClick();
                        smoothScrollTo(href);
                      }}
                    >
                      <Button
                        variant={pathname === href ? "default" : "ghost"}
                        className={`
                          w-full justify-start rounded-xl text-left transition-all duration-300
                          ${pathname === href
                            ? "bg-primary/10 text-primary border border-primary/20"
                            : "hover:bg-accent/10 hover:text-accent"
                          }
                        `}
                      >
                        {label}
                      </Button>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Scroll to Top Button */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            className="fixed bottom-8 right-8 z-40 p-3 rounded-full bg-primary text-primary-foreground shadow-modern-lg hover:shadow-modern-xl transition-all duration-300 hover:scale-110"
            onClick={scrollToTop}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ duration: 0.3 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            aria-label="Scroll to top"
          >
            <ArrowUp className="w-5 h-5" />
          </motion.button>
        )}
      </AnimatePresence>
    </>
  );
}
