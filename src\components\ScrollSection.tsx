'use client';

import { motion } from 'framer-motion';
import { useScrollAnimation, useParallax } from '@/hooks/useScrollAnimation';
import type { BaseComponentProps } from '@/types';

interface ScrollSectionProps extends BaseComponentProps {
  children: React.ReactNode;
  animation?: 'fadeUp' | 'fadeIn' | 'slideLeft' | 'slideRight' | 'scale' | 'parallax';
  delay?: number;
  duration?: number;
  threshold?: number;
  parallaxSpeed?: number;
}

const animationVariants = {
  fadeUp: {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 }
  },
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 }
  },
  slideLeft: {
    initial: { opacity: 0, x: -60 },
    animate: { opacity: 1, x: 0 }
  },
  slideRight: {
    initial: { opacity: 0, x: 60 },
    animate: { opacity: 1, x: 0 }
  },
  scale: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 }
  },
  parallax: {
    initial: { opacity: 0 },
    animate: { opacity: 1 }
  }
};

export function ScrollSection({
  children,
  animation = 'fadeUp',
  delay = 0,
  duration = 0.8,
  threshold = 0.1,
  parallaxSpeed = 0.5,
  className
}: ScrollSectionProps) {
  const { ref: scrollRef, isInView } = useScrollAnimation(threshold);
  const { ref: parallaxRef, y, opacity } = useParallax(parallaxSpeed);

  if (animation === 'parallax') {
    return (
      <motion.div
        ref={parallaxRef}
        style={{ y, opacity }}
        className={className}
      >
        {children}
      </motion.div>
    );
  }

  const variant = animationVariants[animation];

  return (
    <motion.div
      ref={scrollRef}
      initial={variant.initial}
      animate={isInView ? variant.animate : variant.initial}
      transition={{
        duration,
        delay,
        ease: "easeOut"
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Staggered children animation
interface StaggerContainerProps extends BaseComponentProps {
  children: React.ReactNode;
  staggerDelay?: number;
  animation?: 'fadeUp' | 'fadeIn' | 'slideLeft' | 'slideRight' | 'scale';
}

export function StaggerContainer({
  children,
  staggerDelay = 0.1,
  animation = 'fadeUp',
  className
}: StaggerContainerProps) {
  const { ref, isInView } = useScrollAnimation();
  const variant = animationVariants[animation];

  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: staggerDelay
      }
    }
  };

  const itemVariants = {
    initial: variant.initial,
    animate: variant.animate
  };

  return (
    <motion.div
      ref={ref}
      variants={containerVariants}
      initial="initial"
      animate={isInView ? "animate" : "initial"}
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Reveal text animation
interface RevealTextProps extends BaseComponentProps {
  text: string;
  delay?: number;
  duration?: number;
  staggerDelay?: number;
}

export function RevealText({
  text,
  delay = 0,
  duration = 0.8,
  staggerDelay = 0.05,
  className
}: RevealTextProps) {
  const { ref, isInView } = useScrollAnimation();
  const words = text.split(' ');

  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: delay
      }
    }
  };

  const wordVariants = {
    initial: { 
      opacity: 0, 
      y: 20,
      rotateX: -90
    },
    animate: { 
      opacity: 1, 
      y: 0,
      rotateX: 0,
      transition: {
        duration,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      variants={containerVariants}
      initial="initial"
      animate={isInView ? "animate" : "initial"}
      className={`overflow-hidden ${className}`}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          variants={wordVariants}
          className="inline-block mr-2"
          style={{ transformOrigin: "bottom" }}
        >
          {word}
        </motion.span>
      ))}
    </motion.div>
  );
}

// Number counter animation
interface CounterProps extends BaseComponentProps {
  from: number;
  to: number;
  duration?: number;
  delay?: number;
  suffix?: string;
  prefix?: string;
}

export function Counter({
  from,
  to,
  duration = 2,
  delay = 0,
  suffix = '',
  prefix = '',
  className
}: CounterProps) {
  const { ref, isInView } = useScrollAnimation();

  return (
    <motion.div
      ref={ref}
      className={className}
    >
      {isInView && (
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay, duration: 0.5 }}
        >
          <motion.span
            initial={{ textContent: from }}
            animate={{ textContent: to }}
            transition={{
              duration,
              delay,
              ease: "easeOut"
            }}
            onUpdate={(latest) => {
              const value = Math.round(latest.textContent as number);
              if (ref.current) {
                ref.current.textContent = `${prefix}${value}${suffix}`;
              }
            }}
          />
        </motion.span>
      )}
    </motion.div>
  );
}

// Magnetic hover effect
interface MagneticProps extends BaseComponentProps {
  children: React.ReactNode;
  strength?: number;
  disabled?: boolean;
}

export function Magnetic({
  children,
  strength = 0.3,
  disabled = false,
  className
}: MagneticProps) {
  const [position, setPosition] = React.useState({ x: 0, y: 0 });

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (disabled) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * strength;
    const deltaY = (e.clientY - centerY) * strength;
    
    setPosition({ x: deltaX, y: deltaY });
  };

  const handleMouseLeave = () => {
    setPosition({ x: 0, y: 0 });
  };

  return (
    <motion.div
      className={className}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      animate={{ x: position.x, y: position.y }}
      transition={{
        type: "spring",
        stiffness: 200,
        damping: 20,
        mass: 0.5
      }}
    >
      {children}
    </motion.div>
  );
}
